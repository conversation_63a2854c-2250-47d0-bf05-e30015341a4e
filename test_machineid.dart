#!/usr/bin/env dart

import 'dart:io';
import 'get_machineid/lib/get_machineid.dart';

/// Simple test script to verify the get_machineid package functionality
Future<void> main() async {
  print('🔍 Testing get_machineid package...\n');
  
  print('Platform: ${Platform.operatingSystem}');
  print('Platform version: ${Platform.operatingSystemVersion}');
  print('');
  
  try {
    // Test raw machine ID
    print('📱 Getting raw machine ID...');
    final machineId = await GetMachineId.id();
    print('✅ Raw Machine ID: $machineId');
    print('   Length: ${machineId.length} characters');
    print('   Type: ${machineId.runtimeType}');
    print('');
    
    // Test protected machine ID
    print('🔒 Getting protected machine ID...');
    const appId = 'com.test.get_machineid';
    final protectedId = await GetMachineId.protectedId(appId);
    print('✅ Protected Machine ID: $protectedId');
    print('   Length: ${protectedId.length} characters');
    print('   App ID used: $appId');
    print('');
    
    // Verify consistency
    print('🔄 Testing consistency...');
    final machineId2 = await GetMachineId.id();
    final protectedId2 = await GetMachineId.protectedId(appId);
    
    if (machineId == machineId2) {
      print('✅ Raw machine ID is consistent');
    } else {
      print('❌ Raw machine ID is NOT consistent');
    }
    
    if (protectedId == protectedId2) {
      print('✅ Protected machine ID is consistent');
    } else {
      print('❌ Protected machine ID is NOT consistent');
    }
    print('');
    
    // Test different app IDs produce different protected IDs
    print('🔀 Testing different app IDs...');
    final protectedId3 = await GetMachineId.protectedId('com.different.app');
    if (protectedId != protectedId3) {
      print('✅ Different app IDs produce different protected IDs');
    } else {
      print('❌ Different app IDs produce SAME protected IDs (unexpected)');
    }
    print('');
    
    // Validate format
    print('📋 Validating formats...');
    
    // Check that raw ID doesn't contain newlines
    if (!machineId.contains('\n') && !machineId.contains('\r')) {
      print('✅ Raw machine ID format is clean (no newlines)');
    } else {
      print('❌ Raw machine ID contains newlines');
    }
    
    // Check that protected ID is valid hex
    final hexPattern = RegExp(r'^[a-f0-9]{64}$');
    if (hexPattern.hasMatch(protectedId)) {
      print('✅ Protected machine ID is valid 64-character hex string');
    } else {
      print('❌ Protected machine ID is not a valid 64-character hex string');
    }
    
    print('');
    print('🎉 All tests completed successfully!');
    
  } catch (e) {
    print('❌ Error occurred: $e');
    print('');
    print('This might be expected if:');
    print('- Running on an unsupported platform');
    print('- Platform-specific native code is not available');
    print('- Running in a restricted environment');
    exit(1);
  }
}
